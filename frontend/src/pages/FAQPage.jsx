import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import GuaranteeSection from '../components/GuaranteeSection';
import FloatingCTA from '../components/FloatingCTA';

const FAQPage = () => {
  const [openIndex, setOpenIndex] = useState(null);


  // Set page title and meta description
  useEffect(() => {
    document.title = 'FAQ | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Frequently asked questions about VerifiedOnward. Get answers about embassy-approved flight reservations, visa applications, and our service.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
    };
  }, []);

  const faqCategories = [
    {
      title: "Getting Started",
      icon: "🚀",
      faqs: [
        {
          question: "What are embassy-approved flight reservations?",
          answer: "VerifiedOnward provides professional airline reservations with verifiable booking references for visa applications. These are authentic flight reservations that meet embassy requirements and are trusted by immigration offices in 195+ countries worldwide. They show your travel itinerary without requiring you to purchase expensive, non-refundable tickets that could cost $500+."
        },
        {
          question: "How do I get my embassy-approved flight reservation?",
          answer: "Our streamlined 3-step process takes just 60 seconds: 1. Search for flights using our real-time airline data system. 2. Enter passenger details (up to 2 travelers per reservation). 3. Pay the one-time fee of $4.99 via secure payment. 4. Instantly download your professional, embassy-ready reservation document."
        },
        {
          question: "Will my flight reservation be accepted by all embassies?",
          answer: "Yes! Our flight reservations are designed to meet the requirements of embassies and consulates in 195+ countries worldwide. We use authentic airline data and professional formatting that matches real airline reservations, ensuring embassy acceptance for visa applications."
        },
        {
          question: "Can I use this for any type of visa application?",
          answer: "Absolutely! Our flight reservations work for all visa types including tourist, business, student, family visit, and transit visas. Whether you're applying for Schengen, UK, US, Canada, Australia, or any other country's visa, our reservations meet the required standards."
        }
      ]
    },
    {
      title: "Service Details",
      icon: "✈️",
      faqs: [
        {
          question: "Does my reservation show a real flight itinerary?",
          answer: "Absolutely! Your VerifiedOnward reservation displays authentic flight data from global airline systems, including airline name, flight number, departure/arrival times, aircraft type, and duration. The result is a professional, embassy-approved flight reservation with verifiable booking reference that meets all visa application requirements."
        },
        {
          question: "Does my reservation show a valid booking reference?",
          answer: "Yes! VerifiedOnward provides authentic airline reservations with verifiable booking references. This makes our service embassy-trusted and immigration-safe, as the reservations follow official airline formatting standards used by visa officers worldwide."
        },
        {
          question: "What can I use VerifiedOnward flight reservations for?",
          answer: "Our embassy-approved reservations are perfect for: Visa applications requiring flight itinerary proof, demonstrating onward/return travel intent, hotel check-ins requiring travel documentation, immigration interviews, travel insurance applications, and any situation needing verifiable flight booking documentation."
        }
      ]
    },
    {
      title: "Booking & Payment",
      icon: "💳",
      faqs: [
        {
          question: "Can I add multiple passengers to my reservation?",
          answer: "Yes! You can add up to 2 passengers to a single flight reservation at no extra cost. Simply enter their names during booking and all travelers will appear on one shared embassy-approved reservation document. This is perfect for couples, families, or business partners applying for visas together."
        },
        {
          question: "How will I receive my flight reservation?",
          answer: "Immediately after payment, your professional flight reservation will be available for instant download on the confirmation page. A backup copy is automatically sent to your email within 60 seconds. We recommend saving the PDF file to multiple locations for your visa application."
        },
        {
          question: "What if I didn't receive my flight reservation?",
          answer: "No worries! If you didn't receive your reservation file or accidentally deleted it, simply email <NAME_EMAIL> with your order ID or payment receipt. Our support team will resend your reservation within 2 hours during business hours."
        },
        {
          question: "How long is my flight reservation valid?",
          answer: "Your flight reservation remains valid for the duration shown on the document, typically 24-48 hours from the time of creation. This provides sufficient time for your visa application submission. If you need a longer validity period for your specific embassy requirements, please contact our support team."
        },
        {
          question: "Is it legal to use dummy tickets for visa applications?",
          answer: "Yes, it's completely legal and widely accepted practice. Many embassies and consulates actually recommend using flight reservations (rather than purchasing expensive tickets) for visa applications. Our service provides legitimate flight reservations that meet all legal and embassy requirements."
        }
      ]
    }
  ];

  // Flatten all FAQs for display
  const allFaqs = faqCategories.flatMap(category =>
    category.faqs.map(faq => ({ ...faq, category: category.title, icon: category.icon }))
  );

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const ChevronIcon = ({ isOpen }) => (
    <motion.svg
      className="w-6 h-6 text-brand-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      animate={{ rotate: isOpen ? 180 : 0 }}
      transition={{ duration: 0.3 }}
      strokeWidth={2.5}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </motion.svg>
  );

  return (
    <div className="min-h-screen">


      {/* Premium Floating CTA */}
      <FloatingCTA />

      {/* Premium Aviation FAQ Header Section */}
      <section className="relative min-h-screen flex items-center aviation-gradient-hero overflow-hidden">
        {/* Premium Aviation Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/8 via-transparent to-accent-500/8"></div>
        <div className="absolute top-20 left-10 w-80 h-80 bg-brand-400/15 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-400/15 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/10 to-accent-300/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>

        <div className="container-modern text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Premium Aviation Trust Badge */}
            <div className="inline-flex items-center bg-white/90 backdrop-blur-md text-brand-700 px-8 py-4 rounded-2xl text-base font-bold mb-8 shadow-aviation border border-brand-200">
              <div className="w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center mr-3">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              Get Instant Answers • 24/7 Expert Support Available
            </div>

            <h1 className="aviation-hero-text mb-8">
              Frequently Asked{' '}
              <span className="bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent block">
                Questions
              </span>
            </h1>

            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-xl md:text-2xl text-brand-700 leading-relaxed font-medium mb-6">
                Everything you need to know about our professional, embassy-approved flight reservation service.
                <strong className="text-brand-800"> Get expert answers from 75,000+ successful visa applicants.</strong>
              </p>
            </div>



            {/* Premium Aviation Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-16">
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-brand-200">
                <div className="text-4xl font-black text-brand-600 mb-2">12+</div>
                <div className="text-sm font-bold text-brand-700">Common Questions Answered</div>
              </div>
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-accent-200">
                <div className="text-4xl font-black text-accent-600 mb-2">24/7</div>
                <div className="text-sm font-bold text-accent-700">Expert Support Available</div>
              </div>
              <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-aviation border border-green-200">
                <div className="text-4xl font-black text-green-600 mb-2">99.7%</div>
                <div className="text-sm font-bold text-green-700">Embassy Success Rate</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium FAQ Section */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-brand-500/5 to-accent-500/5"></div>
        <div className="absolute top-20 right-10 w-96 h-96 bg-accent-400/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-72 h-72 bg-brand-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="container-modern relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center bg-brand-100 text-brand-700 px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              Most Common Questions Answered
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Get Your
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent block md:inline md:ml-2">
                Answers Instantly
              </span>
            </h2>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-6">
            {allFaqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="premium-card overflow-hidden hover-lift group relative"
              >
                {/* Premium gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative z-10">
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-white/50 transition-all duration-200 focus-modern group-hover:bg-white/30"
                    aria-expanded={openIndex === index}
                    aria-controls={`faq-answer-${index}`}
                  >
                    <h3 className="text-xl font-bold text-neutral-900 pr-4 group-hover:text-brand-600 transition-colors">
                      {faq.question}
                    </h3>
                    <div className="flex-shrink-0">
                      <motion.div
                        animate={{ rotate: openIndex === index ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-8 h-8 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full flex items-center justify-center shadow-glow group-hover:shadow-luxury transition-all duration-300"
                      >
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </motion.div>
                    </div>
                  </button>

                  <AnimatePresence>
                    {openIndex === index && (
                      <motion.div
                        id={`faq-answer-${index}`}
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="px-8 pb-6 pt-2">
                          <div className="border-t border-neutral-200 pt-4">
                            <p className="text-lg text-neutral-700 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Premium shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              </motion.div>
            ))}
          </div>

          {/* Premium Support CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-center mt-16"
          >
            <div className="inline-flex flex-col items-center bg-white/80 backdrop-blur-sm rounded-3xl px-12 py-8 shadow-soft border border-neutral-200 relative overflow-hidden group">
              {/* Background effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-brand-500/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative z-10">
                <h3 className="text-2xl font-bold text-neutral-900 mb-4">
                  Still Have Questions?
                </h3>
                <p className="text-neutral-600 mb-6 max-w-md">
                  Our support team is available 24/7 to help you with your visa application needs
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <Link to="/" className="premium-button inline-flex items-center group">
                    <svg className="w-5 h-5 mr-2 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                    Start Your Reservation
                  </Link>

                  <a href="mailto:<EMAIL>" className="btn-secondary inline-flex items-center group">
                    <svg className="w-5 h-5 mr-2 group-hover:animate-bounce-subtle" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Contact Support
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium Guarantee Section */}
      <GuaranteeSection />
    </div>
  );
};

export default FAQPage;
