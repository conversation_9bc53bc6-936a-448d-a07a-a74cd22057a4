import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getBlogPostBySlug, getAllBlogPosts } from '../data/blogPosts';
import BlogCTA from '../components/BlogCTA';
import UnifiedCTA from '../components/UnifiedCTA';
import { trackBlogView } from '../utils/tracking';
import {
  ClockIcon,
  CalendarIcon,
  TagIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  StarIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  DocumentCheckIcon
} from '@heroicons/react/24/outline';

const BlogPostPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [post, setPost] = useState(null);
  const [relatedPosts, setRelatedPosts] = useState([]);

  useEffect(() => {
    const foundPost = getBlogPostBySlug(slug);
    if (!foundPost) {
      navigate('/blog');
      return;
    }

    setPost(foundPost);

    // Get related posts (other posts with similar tags)
    const allPosts = getAllBlogPosts();
    const related = allPosts
      .filter(p => p.id !== foundPost.id)
      .filter(p => p.tags.some(tag => foundPost.tags.includes(tag)))
      .slice(0, 2);
    setRelatedPosts(related);

    // Track blog view
    trackBlogView(foundPost.title, foundPost.slug);

    // Set page title using metaTitle if available, otherwise use title
    document.title = foundPost.metaTitle || `${foundPost.title} | InstantDummyTicket`;

    // Set meta description using metaDescription if available, otherwise use excerpt
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = foundPost.metaDescription || foundPost.excerpt;

    // Set meta keywords if available
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (foundPost.metaKeywords && foundPost.metaKeywords.length > 0) {
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta');
        metaKeywords.name = 'keywords';
        document.head.appendChild(metaKeywords);
      }
      metaKeywords.content = foundPost.metaKeywords.join(', ');
    }

    // Set Open Graph meta tags for social sharing
    let ogTitle = document.querySelector('meta[property="og:title"]');
    if (!ogTitle) {
      ogTitle = document.createElement('meta');
      ogTitle.setAttribute('property', 'og:title');
      document.head.appendChild(ogTitle);
    }
    ogTitle.content = foundPost.metaTitle || foundPost.title;

    let ogDescription = document.querySelector('meta[property="og:description"]');
    if (!ogDescription) {
      ogDescription = document.createElement('meta');
      ogDescription.setAttribute('property', 'og:description');
      document.head.appendChild(ogDescription);
    }
    ogDescription.content = foundPost.metaDescription || foundPost.excerpt;

    let ogType = document.querySelector('meta[property="og:type"]');
    if (!ogType) {
      ogType = document.createElement('meta');
      ogType.setAttribute('property', 'og:type');
      document.head.appendChild(ogType);
    }
    ogType.content = 'article';

    let ogUrl = document.querySelector('meta[property="og:url"]');
    if (!ogUrl) {
      ogUrl = document.createElement('meta');
      ogUrl.setAttribute('property', 'og:url');
      document.head.appendChild(ogUrl);
    }
    ogUrl.content = `${window.location.origin}/blog/${foundPost.slug}`;

    // Set Twitter Card meta tags
    let twitterCard = document.querySelector('meta[name="twitter:card"]');
    if (!twitterCard) {
      twitterCard = document.createElement('meta');
      twitterCard.name = 'twitter:card';
      document.head.appendChild(twitterCard);
    }
    twitterCard.content = 'summary';

    let twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (!twitterTitle) {
      twitterTitle = document.createElement('meta');
      twitterTitle.name = 'twitter:title';
      document.head.appendChild(twitterTitle);
    }
    twitterTitle.content = foundPost.metaTitle || foundPost.title;

    let twitterDescription = document.querySelector('meta[name="twitter:description"]');
    if (!twitterDescription) {
      twitterDescription = document.createElement('meta');
      twitterDescription.name = 'twitter:description';
      document.head.appendChild(twitterDescription);
    }
    twitterDescription.content = foundPost.metaDescription || foundPost.excerpt;

    // Enhanced JSON-LD structured data for SEO
    const schemaData = {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": foundPost.metaTitle || foundPost.title,
      "description": foundPost.metaDescription || foundPost.excerpt,
      "image": [
        `${window.location.origin}/logo.png`,
        `${window.location.origin}/images/visa-application-guide.jpg`,
        `${window.location.origin}/images/flight-reservation-sample.jpg`
      ],
      "author": {
        "@type": "Organization",
        "name": "VerifiedOnward",
        "url": window.location.origin,
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/logo.png`
        }
      },
      "publisher": {
        "@type": "Organization",
        "name": "VerifiedOnward",
        "url": window.location.origin,
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/logo.png`,
          "width": 200,
          "height": 60
        }
      },
      "datePublished": foundPost.publishDate || foundPost.date,
      "dateModified": foundPost.publishDate || foundPost.date,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${window.location.origin}/blog/${foundPost.slug}`
      },
      "keywords": foundPost.metaKeywords ? foundPost.metaKeywords.join(', ') : foundPost.tags.join(', '),
      "articleSection": "Visa Applications",
      "wordCount": foundPost.content ? foundPost.content.split(' ').length : 0,
      "timeRequired": foundPost.readTime || "5 min read",
      "url": `${window.location.origin}/blog/${foundPost.slug}`,
      "about": {
        "@type": "Thing",
        "name": "Visa Application Flight Requirements",
        "description": "Professional flight reservation services for visa applications"
      },
      "mentions": [
        {
          "@type": "Service",
          "name": "Flight Reservation Service",
          "provider": {
            "@type": "Organization",
            "name": "VerifiedOnward"
          }
        }
      ],
      "isPartOf": {
        "@type": "Blog",
        "name": "VerifiedOnward Visa Guide",
        "url": `${window.location.origin}/blog`
      },
      "inLanguage": "en-US",
      "copyrightYear": new Date().getFullYear(),
      "copyrightHolder": {
        "@type": "Organization",
        "name": "VerifiedOnward"
      }
    };

    // Create and add the JSON-LD script tag
    let schemaScript = document.querySelector('script[type="application/ld+json"]');
    if (schemaScript) {
      schemaScript.remove();
    }

    schemaScript = document.createElement('script');
    schemaScript.type = 'application/ld+json';
    schemaScript.textContent = JSON.stringify(schemaData);
    document.head.appendChild(schemaScript);

    // Cleanup function to reset meta tags when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservations';

      // Reset meta description
      if (metaDescription) {
        metaDescription.content = 'Generate realistic visa-ready flight reservation files instantly for your visa applications. Professional onward flight reservations that meet embassy requirements.';
      }

      // Remove keywords meta tag if it was added
      if (metaKeywords) {
        metaKeywords.remove();
      }

      // Reset Open Graph tags
      if (ogTitle) ogTitle.remove();
      if (ogDescription) ogDescription.remove();
      if (ogType) ogType.remove();
      if (ogUrl) ogUrl.remove();

      // Reset Twitter Card tags
      if (twitterCard) twitterCard.remove();
      if (twitterTitle) twitterTitle.remove();
      if (twitterDescription) twitterDescription.remove();

      // Remove JSON-LD schema script
      const schemaScriptToRemove = document.querySelector('script[type="application/ld+json"]');
      if (schemaScriptToRemove) {
        schemaScriptToRemove.remove();
      }
    };
  }, [slug, navigate]);

  if (!post) {
    return (
      <div className="min-h-screen aviation-gradient-hero flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-neutral-600 text-lg">Loading article...</p>
        </div>
      </div>
    );
  }

  // Function to add internal links to text content
  const addInternalLinks = (text) => {
    // Define internal link patterns and their corresponding routes
    const linkPatterns = [
      { pattern: /\b(how it works|how our service works|our process)\b/gi, link: '/how-it-works', text: 'How It Works' },
      { pattern: /\b(FAQ|frequently asked questions|common questions)\b/gi, link: '/faq', text: 'FAQ' },
      { pattern: /\b(privacy policy|data protection|privacy)\b/gi, link: '/privacy-policy', text: 'Privacy Policy' },
      { pattern: /\b(terms of service|terms and conditions|terms)\b/gi, link: '/terms-of-service', text: 'Terms of Service' },
      { pattern: /\b(refund policy|refunds|money back)\b/gi, link: '/refund-policy', text: 'Refund Policy' },
      { pattern: /\b(search flights|find flights|flight search)\b/gi, link: '/', text: 'Search Flights' },
      { pattern: /\b(blog|resources|visa guides)\b/gi, link: '/blog', text: 'Blog' },
      { pattern: /\b(schengen visa|schengen area visa)\b/gi, link: '/blog/schengen-dummy-ticket-guide', text: 'Schengen Visa Guide' },
      { pattern: /\b(dummy vs real ticket|dummy ticket vs real ticket)\b/gi, link: '/blog/dummy-vs-real-ticket', text: 'Dummy vs Real Ticket Guide' },
      { pattern: /\b(visa rejection|visa denial)\b/gi, link: '/blog/visa-rejection-appeal-process', text: 'Visa Rejection Appeals' },
      { pattern: /\b(embassy requirements|embassy rules)\b/gi, link: '/blog/visa-application-flight-requirements', text: 'Embassy Requirements Guide' }
    ];

    // Process text to add internal links
    let processedText = text;
    const linkElements = [];
    let linkIndex = 0;

    linkPatterns.forEach(({ pattern, link, text: linkText }) => {
      processedText = processedText.replace(pattern, (match) => {
        const placeholder = `__INTERNAL_LINK_${linkIndex}__`;
        linkElements[linkIndex] = { link, text: linkText, originalText: match };
        linkIndex++;
        return placeholder;
      });
    });

    // Split text by placeholders and create React elements
    const parts = processedText.split(/(__INTERNAL_LINK_\d+__)/);

    return parts.map((part, index) => {
      const linkMatch = part.match(/^__INTERNAL_LINK_(\d+)__$/);
      if (linkMatch) {
        const linkIdx = parseInt(linkMatch[1]);
        const linkData = linkElements[linkIdx];
        return (
          <Link
            key={`link-${index}`}
            to={linkData.link}
            className="text-brand-600 hover:text-brand-700 underline font-medium transition-colors"
          >
            {linkData.originalText}
          </Link>
        );
      }
      return part;
    });
  };

  // Function to create testimonial elements
  const createTestimonial = (index) => {
    const testimonials = [
      {
        text: "VerifiedOnward saved my visa application! Got my Schengen visa approved on the first try with their professional flight reservation.",
        author: "Sarah M.",
        country: "UK → Germany",
        rating: 5
      },
      {
        text: "The embassy accepted the flight reservation immediately. Professional format, real flight data - exactly what I needed for my visa application.",
        author: "Ahmed K.",
        country: "UAE → France",
        rating: 5
      },
      {
        text: "Quick, reliable, and embassy-approved. Used it for my tourist visa and got approved within 5 days. Highly recommended!",
        author: "Maria L.",
        country: "Brazil → Spain",
        rating: 5
      }
    ];

    const testimonial = testimonials[index % testimonials.length];

    return (
      <div className="premium-card bg-gradient-to-br from-white to-brand-50/30 p-6 my-8">
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-accent-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-lg">{testimonial.author[0]}</span>
          </div>
          <div className="flex-1">
            <div className="flex items-center mb-2">
              {[...Array(testimonial.rating)].map((_, i) => (
                <StarIcon key={i} className="w-4 h-4 text-yellow-400 fill-current" />
              ))}
            </div>
            <p className="text-neutral-700 italic mb-3">"{testimonial.text}"</p>
            <div className="text-sm text-neutral-600">
              <span className="font-semibold">{testimonial.author}</span>
              <span className="mx-2">•</span>
              <span>{testimonial.country}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Function to create social proof elements
  const createSocialProof = () => {
    return (
      <div className="premium-card bg-gradient-to-r from-green-50/80 to-brand-50/60 p-6 my-8 text-center">
        <div className="flex items-center justify-center space-x-6 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-brand-600">50,000+</div>
            <div className="text-sm text-neutral-600">Successful Applications</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">99.8%</div>
            <div className="text-sm text-neutral-600">Embassy Acceptance</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-accent-600">60s</div>
            <div className="text-sm text-neutral-600">Average Delivery</div>
          </div>
        </div>
        <p className="text-neutral-700 mb-4">Join thousands of successful visa applicants worldwide</p>
        <Link
          to="/"
          className="premium-button group"
        >
          Get Your Reservation Now
          <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
        </Link>
      </div>
    );
  };

  // Function to create callout boxes and visual elements
  const createCalloutBox = (content, type = 'info') => {
    const calloutStyles = {
      info: {
        bg: 'bg-brand-50/80 border-brand-200',
        icon: <CheckCircleIcon className="w-5 h-5 text-brand-500" />,
        title: 'Important Information'
      },
      tip: {
        bg: 'bg-accent-50/80 border-accent-200',
        icon: <StarIcon className="w-5 h-5 text-accent-500" />,
        title: 'Pro Tip'
      },
      warning: {
        bg: 'bg-red-50/80 border-red-200',
        icon: <ShieldCheckIcon className="w-5 h-5 text-red-500" />,
        title: 'Important Notice'
      }
    };

    const style = calloutStyles[type] || calloutStyles.info;

    return (
      <div className={`premium-card ${style.bg} border-l-4 border-l-brand-500 p-6 my-6`}>
        <div className="flex items-start space-x-3">
          {style.icon}
          <div>
            <h4 className="font-semibold text-neutral-900 mb-2">{style.title}</h4>
            <p className="text-neutral-700 leading-relaxed">{content}</p>
          </div>
        </div>
      </div>
    );
  };

  // Function to create visual elements like images and infographics
  const createVisualElement = (type, elementIndex, totalElements) => {
    const progress = elementIndex / totalElements;

    const visualElements = {
      'visa-process': {
        type: 'infographic',
        title: 'Visa Application Process',
        description: 'Step-by-step visual guide to visa applications',
        icon: '📋',
        steps: [
          { step: 1, title: 'Prepare Documents', icon: '📄' },
          { step: 2, title: 'Get Flight Reservation', icon: '✈️' },
          { step: 3, title: 'Submit Application', icon: '📤' },
          { step: 4, title: 'Attend Interview', icon: '🗣️' },
          { step: 5, title: 'Receive Visa', icon: '✅' }
        ]
      },
      'embassy-building': {
        type: 'image',
        src: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=400&fit=crop',
        alt: 'Modern embassy building with flags',
        caption: 'Embassy buildings where visa applications are processed'
      },
      'flight-documents': {
        type: 'image',
        src: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&h=400&fit=crop',
        alt: 'Flight documents and passport on desk',
        caption: 'Professional flight reservation documents for visa applications'
      },
      'airport-scene': {
        type: 'image',
        src: 'https://images.unsplash.com/photo-1556388158-158dc0eca2b8?w=800&h=400&fit=crop',
        alt: 'Busy international airport terminal',
        caption: 'International airport terminal - your journey begins here'
      },
      'document-checklist': {
        type: 'checklist',
        title: 'Essential Visa Documents',
        icon: '✅',
        items: [
          { text: 'Valid passport', checked: true },
          { text: 'Flight reservation', checked: true },
          { text: 'Hotel booking', checked: true },
          { text: 'Travel insurance', checked: false },
          { text: 'Bank statements', checked: false },
          { text: 'Visa application form', checked: false }
        ]
      },
      'success-stats': {
        type: 'stats',
        title: 'Our Success Record',
        stats: [
          { number: '50,000+', label: 'Successful Applications', icon: '🎯' },
          { number: '99.8%', label: 'Embassy Acceptance', icon: '✅' },
          { number: '60 sec', label: 'Average Delivery', icon: '⚡' },
          { number: '24/7', label: 'Customer Support', icon: '🛟' }
        ]
      },
      'comparison-chart': {
        type: 'comparison',
        title: 'Professional vs DIY Flight Reservations',
        items: [
          { feature: 'Embassy Acceptance', professional: '✅ Guaranteed', diy: '❌ Risky' },
          { feature: 'Professional Format', professional: '✅ Airline Standard', diy: '❌ Basic/Fake' },
          { feature: 'Real Flight Data', professional: '✅ Live Airline Data', diy: '❌ Made Up Info' },
          { feature: 'Customer Support', professional: '✅ 24/7 Expert Help', diy: '❌ No Support' },
          { feature: 'Delivery Speed', professional: '✅ 60 Seconds', diy: '❌ Hours/Days' },
          { feature: 'Visa Success Rate', professional: '✅ 99.8%', diy: '❌ 60-70%' }
        ]
      },
      'progress-indicator': {
        type: 'progress',
        title: 'Your Visa Application Progress',
        currentStep: 2,
        steps: [
          { id: 1, title: 'Research Requirements', status: 'complete' },
          { id: 2, title: 'Get Flight Reservation', status: 'current' },
          { id: 3, title: 'Prepare Documents', status: 'pending' },
          { id: 4, title: 'Submit Application', status: 'pending' },
          { id: 5, title: 'Receive Visa', status: 'pending' }
        ]
      },
      'testimonial-visual': {
        type: 'testimonial-card',
        testimonial: {
          text: "VerifiedOnward saved my visa application! Got my Schengen visa approved on the first try with their professional flight reservation.",
          author: "Maria L.",
          country: "Brazil → Spain",
          avatar: "M",
          rating: 5
        }
      },
      'interactive-checklist': {
        type: 'interactive-checklist',
        title: 'Complete Your Visa Application',
        subtitle: 'Check off each step as you progress',
        items: [
          { id: 1, text: 'Research visa requirements for your destination', completed: false },
          { id: 2, text: 'Get professional flight reservation', completed: false },
          { id: 3, text: 'Prepare supporting documents', completed: false },
          { id: 4, text: 'Book accommodation', completed: false },
          { id: 5, text: 'Complete visa application form', completed: false },
          { id: 6, text: 'Schedule embassy appointment', completed: false }
        ]
      },
      'trust-indicators': {
        type: 'trust-visual',
        title: 'Trusted by Embassies Worldwide',
        indicators: [
          { icon: '🏛️', text: 'Embassy Approved Format', subtext: 'Meets official requirements' },
          { icon: '🔒', text: 'Secure & Legal', subtext: 'Fully compliant service' },
          { icon: '⚡', text: 'Instant Delivery', subtext: '60-second processing' },
          { icon: '🌍', text: 'Global Coverage', subtext: 'All countries supported' }
        ]
      },

    };

    // Select appropriate visual based on progress and content
    // Exclude success-stats from frequent rotation to reduce conversion pressure
    const visualKeys = Object.keys(visualElements).filter(key => key !== 'success-stats');
    const selectedIndex = Math.floor(progress * visualKeys.length);
    const selectedKey = visualKeys[Math.min(selectedIndex, visualKeys.length - 1)];

    // Add some variety by cycling through different visuals (excluding success-stats)
    const cycleIndex = elementIndex % visualKeys.length;
    const cycleKey = visualKeys[cycleIndex];

    // Only show success-stats once near the end of the content (80% progress)
    if (progress > 0.8 && elementIndex % 20 === 0) {
      return visualElements['success-stats'];
    }

    // Use cycle key for better distribution
    return visualElements[cycleKey];
  };

  // Function to render visual elements
  const renderVisualElement = (visual, key) => {
    if (visual.type === 'infographic') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-brand-50/50 to-accent-50/30 p-8 my-8">
          <div className="text-center mb-6">
            <div className="text-4xl mb-2">{visual.icon}</div>
            <h3 className="text-xl font-bold text-neutral-900 mb-2">{visual.title}</h3>
            <p className="text-neutral-600">{visual.description}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {visual.steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-brand-500 text-white rounded-full flex items-center justify-center mx-auto mb-2 text-lg font-bold">
                  {step.step}
                </div>
                <div className="text-2xl mb-1">{step.icon}</div>
                <p className="text-sm font-medium text-neutral-700">{step.title}</p>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (visual.type === 'image') {
      return (
        <div key={key} className="premium-card overflow-hidden my-8">
          <img
            src={visual.src}
            alt={visual.alt}
            className="w-full h-64 object-cover"
            loading="lazy"
          />
          <div className="p-4 bg-neutral-50">
            <p className="text-sm text-neutral-600 text-center italic">{visual.caption}</p>
          </div>
        </div>
      );
    }

    if (visual.type === 'checklist') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-green-50/50 to-brand-50/30 p-6 my-8">
          <div className="flex items-center mb-4">
            <span className="text-2xl mr-3">{visual.icon}</span>
            <h3 className="text-lg font-bold text-neutral-900">{visual.title}</h3>
          </div>
          <div className="space-y-3">
            {visual.items.map((item, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-5 h-5 rounded border-2 flex items-center justify-center mr-3 ${
                  item.checked ? 'bg-green-500 border-green-500' : 'border-neutral-300'
                }`}>
                  {item.checked && <span className="text-white text-xs">✓</span>}
                </div>
                <span className={`${item.checked ? 'text-neutral-900 font-medium' : 'text-neutral-600'}`}>
                  {item.text}
                </span>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (visual.type === 'stats') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-accent-50/50 to-brand-50/30 p-8 my-8">
          <h3 className="text-xl font-bold text-neutral-900 text-center mb-6">{visual.title}</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {visual.stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl mb-2">{stat.icon}</div>
                <div className="text-2xl font-bold text-brand-600 mb-1">{stat.number}</div>
                <div className="text-sm text-neutral-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (visual.type === 'comparison') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-blue-50/50 to-green-50/30 p-8 my-8">
          <h3 className="text-xl font-bold text-neutral-900 text-center mb-6">{visual.title}</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-neutral-200">
                  <th className="text-left py-3 px-4 font-semibold text-neutral-700">Feature</th>
                  <th className="text-center py-3 px-4 font-semibold text-green-700">Professional Service</th>
                  <th className="text-center py-3 px-4 font-semibold text-red-700">DIY/Free Options</th>
                </tr>
              </thead>
              <tbody>
                {visual.items.map((item, index) => (
                  <tr key={index} className="border-b border-neutral-100">
                    <td className="py-3 px-4 font-medium text-neutral-800">{item.feature}</td>
                    <td className="py-3 px-4 text-center text-green-700">{item.professional}</td>
                    <td className="py-3 px-4 text-center text-red-700">{item.diy}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    }

    if (visual.type === 'progress') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-brand-50/50 to-accent-50/30 p-8 my-8">
          <h3 className="text-xl font-bold text-neutral-900 text-center mb-6">{visual.title}</h3>
          <div className="flex flex-col md:flex-row items-center justify-between">
            {visual.steps.map((step, index) => (
              <div key={step.id} className="flex flex-col items-center mb-4 md:mb-0">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mb-2 ${
                  step.status === 'complete' ? 'bg-green-500' :
                  step.status === 'current' ? 'bg-brand-500 animate-pulse' :
                  'bg-neutral-300'
                }`}>
                  {step.status === 'complete' ? '✓' : step.id}
                </div>
                <p className={`text-sm text-center font-medium ${
                  step.status === 'current' ? 'text-brand-600' : 'text-neutral-600'
                }`}>
                  {step.title}
                </p>
                {index < visual.steps.length - 1 && (
                  <div className="hidden md:block w-16 h-0.5 bg-neutral-300 mt-6 absolute"
                       style={{left: `${(index + 1) * 20}%`}}></div>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (visual.type === 'testimonial-card') {
      const { testimonial } = visual;
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-yellow-50/50 to-orange-50/30 p-8 my-8">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-brand-500 text-white rounded-full flex items-center justify-center font-bold text-lg">
              {testimonial.avatar}
            </div>
            <div className="flex-1">
              <div className="flex mb-2">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-lg">★</span>
                ))}
              </div>
              <p className="text-neutral-700 italic mb-3">"{testimonial.text}"</p>
              <div className="text-sm">
                <p className="font-semibold text-neutral-900">{testimonial.author}</p>
                <p className="text-neutral-600">{testimonial.country}</p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (visual.type === 'interactive-checklist') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-green-50/50 to-blue-50/30 p-8 my-8">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-neutral-900 mb-2">{visual.title}</h3>
            <p className="text-neutral-600">{visual.subtitle}</p>
          </div>
          <div className="space-y-4">
            {visual.items.map((item, index) => (
              <div key={item.id} className="flex items-center p-3 bg-white/50 rounded-lg hover:bg-white/80 transition-colors duration-200 cursor-pointer group">
                <div className="w-6 h-6 border-2 border-brand-300 rounded flex items-center justify-center mr-4 group-hover:border-brand-500 transition-colors duration-200">
                  <span className="text-brand-500 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200">✓</span>
                </div>
                <span className="text-neutral-700 group-hover:text-neutral-900 transition-colors duration-200">
                  {item.text}
                </span>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-brand-100 text-brand-700 rounded-full text-sm">
              <span className="mr-2">💡</span>
              Hover over items to check them off mentally
            </div>
          </div>
        </div>
      );
    }

    if (visual.type === 'trust-visual') {
      return (
        <div key={key} className="premium-card bg-gradient-to-br from-blue-50/50 to-purple-50/30 p-8 my-8">
          <h3 className="text-xl font-bold text-neutral-900 text-center mb-8">{visual.title}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {visual.indicators.map((indicator, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 bg-white/60 rounded-lg">
                <div className="text-3xl">{indicator.icon}</div>
                <div>
                  <h4 className="font-semibold text-neutral-900 mb-1">{indicator.text}</h4>
                  <p className="text-sm text-neutral-600">{indicator.subtext}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }



    return null;
  };

  // Function to render markdown-like content with enhanced visuals
  const renderContent = (content) => {
    // Simple markdown-like rendering with visual enhancements
    const lines = content.trim().split('\n');
    const elements = [];
    let currentElement = '';
    let elementType = 'p';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.startsWith('# ')) {
        if (currentElement) {
          elements.push({ type: elementType, content: currentElement.trim() });
          currentElement = '';
        }
        elements.push({ type: 'h1', content: line.substring(2) });
      } else if (line.startsWith('## ')) {
        if (currentElement) {
          elements.push({ type: elementType, content: currentElement.trim() });
          currentElement = '';
        }
        elements.push({ type: 'h2', content: line.substring(3) });
      } else if (line.startsWith('### ')) {
        if (currentElement) {
          elements.push({ type: elementType, content: currentElement.trim() });
          currentElement = '';
        }
        elements.push({ type: 'h3', content: line.substring(4) });
      } else if (line === '') {
        if (currentElement) {
          elements.push({ type: elementType, content: currentElement.trim() });
          currentElement = '';
          elementType = 'p';
        }
      } else {
        currentElement += (currentElement ? ' ' : '') + line;
      }
    }

    if (currentElement) {
      elements.push({ type: elementType, content: currentElement.trim() });
    }

    // Add strategic CTAs, testimonials, and social proof throughout content
    const elementsWithCTAs = [];

    // Create contextual CTA messages based on content
    const getContextualCTA = (elementIndex, totalElements, element) => {
      const progress = elementIndex / totalElements;
      const elementContent = element.content?.toLowerCase() || '';

      // Context-aware CTA messages
      if (elementContent.includes('schengen') || elementContent.includes('europe')) {
        return {
          content: "Ready to start your Schengen application? Get your embassy-approved flight reservation now.",
          buttonText: "Get Schengen Flight Reservation"
        };
      } else if (elementContent.includes('embassy') || elementContent.includes('visa officer')) {
        return {
          content: "Ensure your visa approval with professional documentation that embassy officers trust.",
          buttonText: "Get Embassy-Approved Document"
        };
      } else if (elementContent.includes('mistake') || elementContent.includes('avoid') || elementContent.includes('error')) {
        return {
          content: "Don't risk rejection - get a professional flight reservation that meets all embassy requirements.",
          buttonText: "Avoid Visa Mistakes"
        };
      } else if (elementContent.includes('legal') || elementContent.includes('safe') || elementContent.includes('legitimate')) {
        return {
          content: "Join thousands who've successfully used our legitimate, embassy-accepted flight reservations.",
          buttonText: "Get Legal Flight Reservation"
        };
      } else if (progress < 0.3) {
        return {
          content: "Skip the hassle - get your professional flight reservation in 60 seconds.",
          buttonText: "Get Instant Reservation"
        };
      } else if (progress < 0.7) {
        return {
          content: "Ready to secure your visa application? Get your flight reservation now.",
          buttonText: "Secure Your Application"
        };
      } else {
        return {
          content: "Complete your visa application with confidence - get your flight reservation today.",
          buttonText: "Complete Your Application"
        };
      }
    };

    elements.forEach((element, index) => {
      elementsWithCTAs.push(element);

      // Add visual elements at strategic points
      if ((index + 1) % 6 === 0 && index < elements.length - 2) {
        const visual = createVisualElement('visual', index, elements.length);
        elementsWithCTAs.push({
          type: 'visual',
          visual: visual,
          index: index
        });
      }

      // Add testimonial after every 10 elements (reduced frequency)
      else if ((index + 1) % 10 === 0 && index < elements.length - 2) {
        elementsWithCTAs.push({
          type: 'testimonial',
          index: Math.floor(index / 10)
        });
      }

      // Add CTA after every 15 elements (significantly reduced for less intrusive experience)
      else if ((index + 1) % 15 === 0 && index < elements.length - 3) {
        const ctaData = getContextualCTA(index, elements.length, element);
        elementsWithCTAs.push({
          type: 'inline-cta',
          content: ctaData.content,
          buttonText: ctaData.buttonText,
          index: index
        });
      }

      // Add social proof in the middle of the content
      if (index === Math.floor(elements.length / 2)) {
        elementsWithCTAs.push({
          type: 'social-proof'
        });
      }
    });

    return elementsWithCTAs.map((element, index) => {
      const key = `${element.type}-${index}`;

      if (element.type === 'visual') {
        return renderVisualElement(element.visual, key);
      }

      if (element.type === 'inline-cta') {
        return (
          <div key={key} className="premium-card bg-gradient-to-r from-brand-50/80 to-accent-50/60 p-6 my-8 text-center">
            <p className="text-neutral-700 mb-4">{element.content}</p>
            <Link
              to="/"
              className="premium-button group"
            >
              {element.buttonText || "Get Flight Reservation"}
              <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        );
      }

      if (element.type === 'testimonial') {
        return createTestimonial(element.index);
      }

      if (element.type === 'social-proof') {
        return createSocialProof();
      }

      switch (element.type) {
        case 'h1':
          return (
            <h1 key={key} className="heading-secondary text-neutral-900 mb-6 mt-8 first:mt-0 bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent">
              {element.content}
            </h1>
          );
        case 'h2':
          return (
            <h2 key={key} className="heading-tertiary text-neutral-900 mb-4 mt-8 border-l-4 border-brand-500 pl-4">
              {element.content}
            </h2>
          );
        case 'h3':
          return (
            <h3 key={key} className="text-xl font-semibold text-neutral-800 mb-3 mt-6 flex items-center">
              <CheckCircleIcon className="w-5 h-5 text-brand-500 mr-2" />
              {element.content}
            </h3>
          );
        default:
          // Check for special content patterns
          if (element.content.includes('✅') || element.content.includes('✓')) {
            // Create a checklist item
            return (
              <div key={key} className="premium-card bg-green-50/50 border-green-200 p-4 mb-4">
                <p className="text-body text-neutral-700 leading-relaxed flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  {element.content.replace(/[✅✓]/g, '').trim().split('**').map((part, i) => {
                    if (i % 2 === 1) {
                      return <strong key={i} className="font-semibold text-neutral-900">{part}</strong>;
                    } else {
                      const linkedContent = addInternalLinks(part);
                      return <span key={i}>{linkedContent}</span>;
                    }
                  })}
                </p>
              </div>
            );
          }

          // Check for important notes or tips
          if (element.content.toLowerCase().includes('important:') || element.content.toLowerCase().includes('note:')) {
            return createCalloutBox(element.content, 'info');
          }

          if (element.content.toLowerCase().includes('tip:') || element.content.toLowerCase().includes('pro tip:')) {
            return createCalloutBox(element.content, 'tip');
          }

          if (element.content.toLowerCase().includes('warning:') || element.content.toLowerCase().includes('caution:')) {
            return createCalloutBox(element.content, 'warning');
          }

          // Regular paragraph with enhanced styling
          return (
            <p key={key} className="text-body text-neutral-700 mb-6 leading-relaxed">
              {element.content.split('**').map((part, i) => {
                if (i % 2 === 1) {
                  return <strong key={i} className="font-semibold text-neutral-900 bg-brand-50 px-1 rounded">{part}</strong>;
                } else {
                  // Process the text part for internal links
                  const linkedContent = addInternalLinks(part);
                  return <span key={i}>{linkedContent}</span>;
                }
              })}
            </p>
          );
      }
    });
  };

  // Floating sidebar CTA component
  const FloatingSidebarCTA = () => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
      const handleScroll = () => {
        const scrollPosition = window.scrollY;
        setIsVisible(scrollPosition > 300);
      };

      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    if (!isVisible) return null;

    return (
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 100 }}
        className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50 max-w-xs"
      >
        <div className="premium-card bg-gradient-to-br from-brand-500 to-brand-600 text-white p-6 shadow-luxury">
          <div className="text-center">
            <div className="text-2xl mb-2">⚡</div>
            <h4 className="font-bold mb-2">Ready to Apply?</h4>
            <p className="text-sm mb-4 opacity-90">Get your flight reservation in 60 seconds</p>
            <Link
              to="/"
              className="block w-full bg-white text-brand-600 font-bold py-2 px-4 rounded-lg hover:bg-brand-50 transition-colors duration-300"
            >
              Get Started →
            </Link>
          </div>
          <div className="mt-4 text-xs text-center opacity-75">
            ✓ 99.8% Embassy Acceptance
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen aviation-gradient-hero relative">
      {/* Premium Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>

      {/* Floating Sidebar CTA */}
      <FloatingSidebarCTA />

      {/* Breadcrumb Navigation */}
      <motion.div
        className="premium-card bg-white/80 backdrop-blur-sm border-brand-100/50 shadow-aviation relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container-modern py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-brand-600 hover:text-brand-700 transition-colors font-medium">
              Home
            </Link>
            <ArrowRightIcon className="w-4 h-4 text-neutral-400" />
            <Link to="/blog" className="text-brand-600 hover:text-brand-700 transition-colors font-medium">
              Blog
            </Link>
            <ArrowRightIcon className="w-4 h-4 text-neutral-400" />
            <span className="text-neutral-600 truncate">{post.title}</span>
          </nav>
        </div>
      </motion.div>

      {/* Article Content */}
      <motion.article
        className="container-modern section-padding relative z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="premium-card overflow-hidden">
          {/* Article Header */}
          <div className="p-8 border-b border-brand-100/50 bg-gradient-to-r from-brand-50/50 to-accent-50/30">
            {/* Premium Tags */}
            <div className="flex flex-wrap gap-3 mb-6">
              {post.tags.map((tag, index) => (
                <span
                  key={index}
                  className="trust-badge"
                >
                  <TagIcon className="w-4 h-4 mr-1" />
                  {tag}
                </span>
              ))}
            </div>

            {/* Premium Title */}
            <h1 className="heading-primary mb-6 bg-gradient-to-r from-neutral-900 via-brand-700 to-accent-700 bg-clip-text text-transparent">
              {post.title}
            </h1>

            {/* Premium Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-neutral-600 mb-6">
              <div className="flex items-center">
                <CalendarIcon className="w-5 h-5 mr-2 text-brand-500" />
                <span className="font-medium">{new Date(post.publishDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</span>
              </div>
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 mr-2 text-accent-500" />
                <span className="font-medium">{post.readTime}</span>
              </div>
              <div className="flex items-center">
                <StarIcon className="w-5 h-5 mr-2 text-yellow-500" />
                <span className="font-medium">Expert Guide</span>
              </div>
            </div>

            {/* Premium Excerpt */}
            <div className="premium-card bg-white/60 backdrop-blur-sm p-6 border-l-4 border-brand-500">
              <p className="text-body-large text-neutral-700 leading-relaxed font-medium">
                {post.excerpt}
              </p>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap gap-4 mt-6">
              <div className="flex items-center text-sm text-neutral-600">
                <ShieldCheckIcon className="w-4 h-4 mr-2 text-green-500" />
                Embassy Approved
              </div>
              <div className="flex items-center text-sm text-neutral-600">
                <DocumentCheckIcon className="w-4 h-4 mr-2 text-brand-500" />
                Expert Verified
              </div>
              <div className="flex items-center text-sm text-neutral-600">
                <GlobeAltIcon className="w-4 h-4 mr-2 text-accent-500" />
                Global Coverage
              </div>
            </div>
          </div>

          {/* Article Body */}
          <div className="p-8">
            <div className="prose prose-lg max-w-none">
              {renderContent(post.content)}
            </div>
          </div>
        </div>
      </motion.article>

      {/* Premium Blog CTA */}
      <div className="container-modern relative z-10">
        <BlogCTA />
      </div>

      {/* Premium Related Posts */}
      {relatedPosts.length > 0 && (
        <motion.div
          className="container-modern pb-12 relative z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="premium-card">
            <div className="p-8">
              <h3 className="heading-secondary text-neutral-900 mb-8 text-center">
                Continue Your Journey
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {relatedPosts.map((relatedPost) => (
                  <Link
                    key={relatedPost.id}
                    to={`/blog/${relatedPost.slug}`}
                    className="premium-card bg-gradient-to-br from-white to-brand-50/30 p-6 hover:-translate-y-2 transition-all duration-300 group"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-brand-500 to-accent-500 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                        <DocumentCheckIcon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-neutral-900 mb-2 group-hover:text-brand-600 transition-colors line-clamp-2">
                          {relatedPost.title}
                        </h4>
                        <p className="text-neutral-600 text-sm mb-3 line-clamp-2">
                          {relatedPost.excerpt}
                        </p>
                        <div className="flex items-center text-xs text-neutral-500">
                          <ClockIcon className="w-3 h-3 mr-1" />
                          <span>{relatedPost.readTime}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Premium Call to Action */}
      <div className="relative z-10">
        <UnifiedCTA />
      </div>
    </div>
  );
};

export default BlogPostPage;
