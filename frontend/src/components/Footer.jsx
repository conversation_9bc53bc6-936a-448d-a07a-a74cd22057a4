import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const Footer = () => {
  return (
    <motion.footer
      className="relative bg-gradient-to-br from-brand-900 via-brand-800 to-neutral-900 text-white overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5, duration: 0.5 }}
    >
      {/* Premium Aviation background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-500/15 via-transparent to-accent-500/15"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-brand-400/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>

      {/* Subtle Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.03]" style={{
        backgroundImage: `radial-gradient(circle at 1px 1px, rgb(14 165 233) 1px, transparent 0)`,
        backgroundSize: '40px 40px'
      }}></div>
      <div className="container-modern section-padding relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Premium Company Info */}
          <div className="col-span-1 lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="premium-card bg-white/5 backdrop-blur-sm border-white/10 p-8 hover:bg-white/10 transition-all duration-300 group"
            >
              {/* Premium Aviation logo section */}
              <div className="flex items-center space-x-6 mb-10">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-600 rounded-3xl flex items-center justify-center shadow-aviation group-hover:shadow-aviation-hover transition-all duration-300 transform group-hover:scale-105">
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                    </svg>
                  </div>
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  {/* Premium aviation glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-brand-500/40 to-brand-600/40 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div>
                  <h3 className="text-4xl font-black bg-gradient-to-r from-white via-brand-200 to-accent-200 bg-clip-text text-transparent">
                    VerifiedOnward
                  </h3>
                  <p className="text-brand-300 font-bold text-lg">Professional Flight Reservations</p>
                  <div className="flex items-center space-x-3 mt-3">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-300 font-bold">Trusted by 75,000+ Travelers Worldwide</span>
                  </div>
                </div>
              </div>

              <p className="text-neutral-200 leading-relaxed text-xl mb-8 font-medium">
                Get professional airline reservations with verifiable booking references in 60 seconds. Embassy-trusted and immigration-safe
                flight documents that meet all visa application requirements across 195+ countries.
              </p>

              {/* Premium Aviation stats */}
              <div className="grid grid-cols-3 gap-6">
                <div className="text-center p-4 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-black text-green-400">99.7%</div>
                  <div className="text-sm text-green-300 font-bold">Embassy Success</div>
                </div>
                <div className="text-center p-4 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-black text-brand-400">75,000+</div>
                  <div className="text-sm text-brand-300 font-bold">Happy Travelers</div>
                </div>
                <div className="text-center p-4 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-black text-accent-400">60s</div>
                  <div className="text-sm text-accent-300 font-bold">Instant Download</div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Premium Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="premium-card bg-white/5 backdrop-blur-sm border-white/10 p-6 hover:bg-white/10 transition-all duration-300"
          >
            <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-brand-300 to-accent-300 bg-clip-text text-transparent">
              Quick Links
            </h4>
            <ul className="space-y-4">
              {[
                { to: "/how-it-works", label: "How It Works", icon: "⚡" },
                { to: "/use-cases", label: "Use Cases", icon: "💼" },
                { to: "/faq", label: "FAQ", icon: "❓" },
                { to: "/blog", label: "Blog / Resources", icon: "📚" },
                { to: "/privacy-policy", label: "Privacy Policy", icon: "🔒" },
                { to: "/terms-of-service", label: "Terms of Service", icon: "📋" }
              ].map((link, index) => (
                <motion.li
                  key={link.to}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Link
                    to={link.to}
                    className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-xl px-3 py-2 transition-all duration-300 group"
                  >
                    <span className="text-sm group-hover:scale-110 transition-transform duration-200">{link.icon}</span>
                    <span className="group-hover:translate-x-1 transition-transform duration-200">{link.label}</span>
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Premium Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="premium-card bg-white/5 backdrop-blur-sm border-white/10 p-6 hover:bg-white/10 transition-all duration-300"
          >
            <h4 className="text-xl font-bold mb-6 bg-gradient-to-r from-accent-300 to-brand-300 bg-clip-text text-transparent">
              Support
            </h4>
            <ul className="space-y-4">
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Link
                  to="/contact"
                  className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-xl px-3 py-2 transition-all duration-300 group"
                >
                  <span className="text-sm group-hover:scale-110 transition-transform duration-200">📞</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">Contact</span>
                </Link>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.05 }}
              >
                <a
                  href="mailto:<EMAIL>?subject=Support%20Request&body=Hello%20VerifiedOnward%20Support%20Team,%0A%0AI%20need%20assistance%20with:%0A%0A"
                  className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-xl px-3 py-2 transition-all duration-300 group"
                >
                  <span className="text-sm group-hover:scale-110 transition-transform duration-200">📧</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">Email Support</span>
                </a>
              </motion.li>
              <motion.li
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <Link
                  to="/refund-policy"
                  className="flex items-center space-x-3 text-neutral-300 hover:text-white hover:bg-white/5 rounded-xl px-3 py-2 transition-all duration-300 group"
                >
                  <span className="text-sm group-hover:scale-110 transition-transform duration-200">📋</span>
                  <span className="group-hover:translate-x-1 transition-transform duration-200">Sales Policy</span>
                </Link>
              </motion.li>
            </ul>

            {/* Premium contact CTA */}
            <div className="mt-6 pt-6 border-t border-white/10">
              <motion.a
                href="mailto:<EMAIL>"
                className="block premium-button text-center text-sm relative overflow-hidden group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <span className="relative z-10">Get Help Now</span>
              </motion.a>
            </div>
          </motion.div>
        </div>

        {/* Premium Trust Badges Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="border-t border-white/10 mt-16 pt-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-brand-300 to-accent-300 bg-clip-text text-transparent mb-2">
              Trusted & Secure
            </h3>
            <p className="text-neutral-400">Your data and payments are protected by industry-leading security</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            {[
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ),
                title: "Verified by Stripe",
                desc: "Secure payment processing",
                color: "from-accent-500 to-accent-600"
              },
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                ),
                title: "PayPal Verified",
                desc: "Trusted payment partner",
                color: "from-brand-500 to-brand-600"
              },
              {
                icon: (
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                ),
                title: "100% SSL Secured",
                desc: "Bank-level encryption",
                color: "from-accent-500 to-brand-500"
              }
            ].map((badge, index) => (
              <motion.div
                key={badge.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="premium-card bg-white/5 backdrop-blur-sm border-white/10 p-6 text-center hover:bg-white/10 transition-all duration-300 group"
              >
                <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${badge.color} rounded-2xl flex items-center justify-center text-white shadow-glow group-hover:shadow-luxury transition-all duration-300`}>
                  {badge.icon}
                </div>
                <h4 className="text-lg font-bold text-white mb-2">{badge.title}</h4>
                <p className="text-neutral-400 text-sm">{badge.desc}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Premium Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-white/10 pt-8"
        >
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-6">
              <p className="text-neutral-400 text-sm">
                © 2025 VerifiedOnward.com. All rights reserved.
              </p>
              <div className="flex items-center space-x-4 text-xs text-neutral-500">
                <span className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-accent-400 rounded-full animate-pulse"></div>
                  <span>Live Support Available</span>
                </span>
                <span>•</span>
                <span>99.7% Embassy Acceptance Rate</span>
                <span>•</span>
                <span>Professional Quality Standards</span>
              </div>
            </div>

            {/* Premium social proof */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-neutral-400 text-sm">
                <span>⭐⭐⭐⭐⭐</span>
                <span>4.9/5 from 12,847 reviews</span>
              </div>
            </div>
          </div>

          {/* Premium final CTA */}
          <div className="mt-8 text-center">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/"
                className="inline-flex items-center space-x-3 premium-button-large relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                <span className="relative z-10 flex items-center space-x-2">
                  <span>🚀</span>
                  <span>Get Your $4.99 Visa Document Now</span>
                  <span>✈️</span>
                </span>
              </Link>
            </motion.div>
            <p className="text-neutral-500 text-xs mt-3">
              Join 75,000+ travelers who trust VerifiedOnward for their visa applications
            </p>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;
